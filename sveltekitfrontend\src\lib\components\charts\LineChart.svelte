<script lang="ts">
	import { onMount } from 'svelte';

	interface Props {
		data: Array<{ x: string | number; y: number; label?: string }>;
		title: string;
		xLabel?: string;
		yLabel?: string;
		color?: string;
		height?: number;
	}

	let {
		data,
		title,
		xLabel = 'X',
		yLabel = 'Y',
		color = 'var(--theme2)',
		height = 300
	}: Props = $props();

	let canvas: HTMLCanvasElement;
	let ctx: CanvasRenderingContext2D;

	onMount(() => {
		ctx = canvas.getContext('2d')!;
		drawChart();
	});

	$effect(() => {
		if (ctx && data) {
			drawChart();
		}
	});

	function drawChart() {
		if (!ctx || !data || data.length === 0) return;

		const { width, height: canvasHeight } = canvas;
		ctx.clearRect(0, 0, width, canvasHeight);

		// Set up margins
		const margin = { top: 40, right: 40, bottom: 60, left: 60 };
		const chartWidth = width - margin.left - margin.right;
		const chartHeight = canvasHeight - margin.top - margin.bottom;

		// Find min/max values
		const maxY = Math.max(...data.map((d) => d.y));
		const minY = Math.min(...data.map((d) => d.y));
		const yRange = maxY - minY || 1;

		// Set up scales
		const xStep = chartWidth / (data.length - 1 || 1);

		// Draw background
		ctx.fillStyle = 'var(--bg-dark)';
		ctx.fillRect(margin.left, margin.top, chartWidth, chartHeight);

		// Draw grid lines
		ctx.strokeStyle = 'var(--border-color)';
		ctx.lineWidth = 1;

		// Horizontal grid lines
		for (let i = 0; i <= 5; i++) {
			const y = margin.top + (chartHeight / 5) * i;
			ctx.beginPath();
			ctx.moveTo(margin.left, y);
			ctx.lineTo(margin.left + chartWidth, y);
			ctx.stroke();
		}

		// Vertical grid lines
		for (let i = 0; i < data.length; i += Math.ceil(data.length / 8)) {
			const x = margin.left + xStep * i;
			ctx.beginPath();
			ctx.moveTo(x, margin.top);
			ctx.lineTo(x, margin.top + chartHeight);
			ctx.stroke();
		}

		// Draw line
		ctx.strokeStyle = color;
		ctx.lineWidth = 3;
		ctx.beginPath();

		data.forEach((point, index) => {
			const x = margin.left + xStep * index;
			const y = margin.top + chartHeight - ((point.y - minY) / yRange) * chartHeight;

			if (index === 0) {
				ctx.moveTo(x, y);
			} else {
				ctx.lineTo(x, y);
			}
		});

		ctx.stroke();

		// Draw points
		ctx.fillStyle = color;
		data.forEach((point, index) => {
			const x = margin.left + xStep * index;
			const y = margin.top + chartHeight - ((point.y - minY) / yRange) * chartHeight;

			ctx.beginPath();
			ctx.arc(x, y, 4, 0, 2 * Math.PI);
			ctx.fill();
		});

		// Draw axes
		ctx.strokeStyle = 'var(--text)';
		ctx.lineWidth = 2;

		// Y-axis
		ctx.beginPath();
		ctx.moveTo(margin.left, margin.top);
		ctx.lineTo(margin.left, margin.top + chartHeight);
		ctx.stroke();

		// X-axis
		ctx.beginPath();
		ctx.moveTo(margin.left, margin.top + chartHeight);
		ctx.lineTo(margin.left + chartWidth, margin.top + chartHeight);
		ctx.stroke();

		// Draw labels
		ctx.fillStyle = 'var(--text)';
		ctx.font = '12px sans-serif';
		ctx.textAlign = 'center';

		// X-axis labels
		data.forEach((point, index) => {
			if (index % Math.ceil(data.length / 8) === 0) {
				const x = margin.left + xStep * index;
				const label = point.label || String(point.x);
				ctx.fillText(label, x, margin.top + chartHeight + 20);
			}
		});

		// Y-axis labels
		ctx.textAlign = 'right';
		for (let i = 0; i <= 5; i++) {
			const value = minY + (yRange / 5) * (5 - i);
			const y = margin.top + (chartHeight / 5) * i + 4;
			ctx.fillText(value.toFixed(0), margin.left - 10, y);
		}

		// Draw title
		ctx.fillStyle = 'var(--text)';
		ctx.font = 'bold 16px sans-serif';
		ctx.textAlign = 'center';
		ctx.fillText(title, width / 2, 25);

		// Draw axis labels
		ctx.font = '14px sans-serif';
		ctx.fillText(xLabel, width / 2, canvasHeight - 10);

		ctx.save();
		ctx.translate(15, canvasHeight / 2);
		ctx.rotate(-Math.PI / 2);
		ctx.fillText(yLabel, 0, 0);
		ctx.restore();
	}

	function handleResize() {
		const rect = canvas.parentElement?.getBoundingClientRect();
		if (rect) {
			canvas.width = rect.width;
			canvas.height = height;
			drawChart();
		}
	}

	onMount(() => {
		handleResize();
		window.addEventListener('resize', handleResize);
		return () => window.removeEventListener('resize', handleResize);
	});
</script>

<div class="chart-container">
	<canvas bind:this={canvas} style="width: 100%; height: {height}px;"></canvas>
</div>

<style>
	.chart-container {
		width: 100%;
		height: 100%;
		background: var(--bg-bright);
		border-radius: 8px;
		padding: 1rem;
		border: 1px solid var(--border-color);
	}

	canvas {
		display: block;
	}
</style>
