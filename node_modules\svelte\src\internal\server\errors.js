/* This file is generated by scripts/process-messages/index.js. Do not edit! */



/**
 * `%name%(...)` is not available on the server
 * @param {string} name
 * @returns {never}
 */
export function lifecycle_function_unavailable(name) {
	const error = new Error(`lifecycle_function_unavailable\n\`${name}(...)\` is not available on the server\nhttps://svelte.dev/e/lifecycle_function_unavailable`);

	error.name = 'Svelte error';

	throw error;
}