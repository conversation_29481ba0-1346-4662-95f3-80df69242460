<script lang="ts">
	import { onMount } from 'svelte';

	interface Props {
		data: Array<{ hour: number; messageCount: number; uniqueUsers: number }>;
		title: string;
		height?: number;
	}

	let { data, title, height = 300 }: Props = $props();

	let canvas: HTMLCanvasElement;
	let ctx: CanvasRenderingContext2D;

	onMount(() => {
		ctx = canvas.getContext('2d')!;
		drawChart();
	});

	$effect(() => {
		if (ctx && data) {
			drawChart();
		}
	});

	function drawChart() {
		if (!ctx || !data || data.length === 0) return;

		const { width, height: canvasHeight } = canvas;
		ctx.clearRect(0, 0, width, canvasHeight);

		// Set up margins
		const margin = { top: 40, right: 40, bottom: 60, left: 60 };
		const chartWidth = width - margin.left - margin.right;
		const chartHeight = canvasHeight - margin.top - margin.bottom;

		// Create 24-hour data array (fill missing hours with 0)
		const hourlyData = Array.from({ length: 24 }, (_, hour) => {
			const found = data.find((d) => d.hour === hour);
			return found || { hour, messageCount: 0, uniqueUsers: 0 };
		});

		// Find max values for color scaling
		const maxMessages = Math.max(...hourlyData.map((d) => d.messageCount));
		const maxUsers = Math.max(...hourlyData.map((d) => d.uniqueUsers));

		// Calculate cell dimensions
		const cellWidth = chartWidth / 24;
		const cellHeight = chartHeight / 2; // Two rows: messages and users

		// Draw background
		ctx.fillStyle = 'var(--bg-dark)';
		ctx.fillRect(margin.left, margin.top, chartWidth, chartHeight);

		// Draw heatmap cells
		hourlyData.forEach((point, index) => {
			const x = margin.left + index * cellWidth;

			// Messages row
			const messageIntensity = maxMessages > 0 ? point.messageCount / maxMessages : 0;
			const messageColor = getHeatmapColor(messageIntensity);
			ctx.fillStyle = messageColor;
			ctx.fillRect(x, margin.top, cellWidth - 1, cellHeight - 1);

			// Users row
			const userIntensity = maxUsers > 0 ? point.uniqueUsers / maxUsers : 0;
			const userColor = getHeatmapColor(userIntensity);
			ctx.fillStyle = userColor;
			ctx.fillRect(x, margin.top + cellHeight, cellWidth - 1, cellHeight - 1);

			// Draw hour labels
			if (index % 2 === 0) {
				ctx.fillStyle = 'var(--text)';
				ctx.font = '10px sans-serif';
				ctx.textAlign = 'center';
				ctx.fillText(
					point.hour.toString().padStart(2, '0'),
					x + cellWidth / 2,
					margin.top + chartHeight + 15
				);
			}
		});

		// Draw row labels
		ctx.fillStyle = 'var(--text)';
		ctx.font = '12px sans-serif';
		ctx.textAlign = 'right';
		ctx.fillText('Messages', margin.left - 10, margin.top + cellHeight / 2 + 4);
		ctx.fillText('Users', margin.left - 10, margin.top + cellHeight + cellHeight / 2 + 4);

		// Draw title
		ctx.fillStyle = 'var(--text)';
		ctx.font = 'bold 16px sans-serif';
		ctx.textAlign = 'center';
		ctx.fillText(title, width / 2, 25);

		// Draw legend
		drawLegend(ctx, width - 150, margin.top, maxMessages, maxUsers);
	}

	function getHeatmapColor(intensity: number): string {
		// Create a color gradient from dark blue to bright yellow
		const colors = [
			[18, 24, 27], // Dark background
			[30, 60, 100], // Dark blue
			[50, 120, 180], // Medium blue
			[100, 180, 220], // Light blue
			[150, 220, 255], // Very light blue
			[255, 255, 150], // Light yellow
			[255, 200, 50] // Bright yellow
		];

		const scaledIntensity = Math.min(intensity, 1);
		const colorIndex = scaledIntensity * (colors.length - 1);
		const lowerIndex = Math.floor(colorIndex);
		const upperIndex = Math.ceil(colorIndex);
		const factor = colorIndex - lowerIndex;

		if (lowerIndex === upperIndex) {
			const [r, g, b] = colors[lowerIndex];
			return `rgb(${r}, ${g}, ${b})`;
		}

		const [r1, g1, b1] = colors[lowerIndex];
		const [r2, g2, b2] = colors[upperIndex];

		const r = Math.round(r1 + (r2 - r1) * factor);
		const g = Math.round(g1 + (g2 - g1) * factor);
		const b = Math.round(b1 + (b2 - b1) * factor);

		return `rgb(${r}, ${g}, ${b})`;
	}

	function drawLegend(
		ctx: CanvasRenderingContext2D,
		x: number,
		y: number,
		maxMessages: number,
		maxUsers: number
	) {
		const legendWidth = 100;
		const legendHeight = 15;

		// Draw gradient bar
		for (let i = 0; i < legendWidth; i++) {
			const intensity = i / legendWidth;
			ctx.fillStyle = getHeatmapColor(intensity);
			ctx.fillRect(x + i, y, 1, legendHeight);
		}

		// Draw legend labels
		ctx.fillStyle = 'var(--text)';
		ctx.font = '10px sans-serif';
		ctx.textAlign = 'left';
		ctx.fillText('0', x, y + legendHeight + 12);
		ctx.textAlign = 'right';
		ctx.fillText('Max', x + legendWidth, y + legendHeight + 12);

		// Draw legend title
		ctx.textAlign = 'center';
		ctx.fillText('Activity Intensity', x + legendWidth / 2, y - 5);
	}

	function handleResize() {
		const rect = canvas.parentElement?.getBoundingClientRect();
		if (rect) {
			canvas.width = rect.width;
			canvas.height = height;
			drawChart();
		}
	}

	onMount(() => {
		handleResize();
		window.addEventListener('resize', handleResize);
		return () => window.removeEventListener('resize', handleResize);
	});
</script>

<div class="chart-container">
	<canvas bind:this={canvas} style="width: 100%; height: {height}px;"></canvas>
</div>

<style>
	.chart-container {
		width: 100%;
		height: 100%;
		background: var(--bg-bright);
		border-radius: 8px;
		padding: 1rem;
		border: 1px solid var(--border-color);
	}

	canvas {
		display: block;
	}
</style>
