import { browser } from '$app/environment';
import { dev } from '$app/environment';

// API configuration
export const API_CONFIG = {
	// In development, use the dev server proxy
	// In production, use the backend directly
	baseUrl: dev 
		? '/api'  // This will be proxied by Vite dev server
		: 'http://localhost:8025'  // Direct backend URL for production
};

// Helper function to build API URLs
export function apiUrl(path: string): string {
	// Remove leading slash if present to avoid double slashes
	const cleanPath = path.startsWith('/') ? path.slice(1) : path;
	return `${API_CONFIG.baseUrl}/${cleanPath}`;
}

// Helper function for making API requests
export async function apiRequest(path: string, options: RequestInit = {}): Promise<Response> {
	const url = apiUrl(path);
	
	const defaultOptions: RequestInit = {
		headers: {
			'Content-Type': 'application/json',
			...options.headers,
		},
		...options,
	};

	return fetch(url, defaultOptions);
}
