// From web/src/types/log.ts
export interface UserLogResponse {
	messages: BasicLogMessage[];
}

export interface BasicLogMessage {
	text: string;
	displayName: string;
	timestamp: string; // The raw timestamp from the API is a string
	id: string;
	tags: Record<string, string>;
}

// A processed message with a Date object for easier handling in the UI
export interface LogMessage extends Omit<BasicLogMessage, 'timestamp'> {
	timestamp: Date;
	emotes: Emote[];
}

// A processed full message, also with a Date object
export interface FullLogMessage extends Omit<BasicLogMessage, 'timestamp'> {
	timestamp: Date;
	username: string;
	channel: string;
	raw: string;
	type: number;
	emotes?: Emote[];
}

export interface Emote {
	startIndex: number;
	endIndex: number;
	code: string;
	id: string;
}

// From web/src/hooks/useChannels.ts
export interface Channel {
	userID: string;
	name: string;
}

// From web/src/hooks/useAvailableLogs.ts
export interface AvailableLog {
	month: string;
	year: string;
}
export type AvailableLogs = AvailableLog[];

// From web/src/hooks/useAdvancedSearch.ts & useGlobalSearch.ts
export interface AdvancedSearchParams {
	q?: string;
	regex?: boolean;
	caseSensitive?: boolean;
	channels?: string[];
	excludeChannels?: string[];
	users?: string[];
	excludeUsers?: string[];
	messageTypes?: string[];
	badges?: string[];
	from?: Date | string | null;
	to?: Date | string | null;
	minLength?: number;
	maxLength?: number;
	firstMessagesOnly?: boolean;
	subscribersOnly?: boolean;
	vipsOnly?: boolean;
	moderatorsOnly?: boolean;
	hasEmotes?: boolean;
	searchDisplayNames?: boolean;
}

// From web/src/components/UserTracker.tsx
export interface TrackedUser {
	userId: string;
	username: string;
	addedAt: Date;
}

// From backend API for analytics
export interface GlobalAnalytics {
	totalChannels: number;
	totalMessages: number;
	totalUsers: number;
	dateRange: { start: string; end: string };
	topChannels: ChannelSummary[];
	topUsers: UserSummary[];
	messageDistribution: MessageTypeCount[];
	activityHeatmap: HourlyActivity[];
	growthMetrics: GrowthMetrics;
}

export interface ChannelSummary {
	channelId: string;
	channelName: string;
	messageCount: number;
	uniqueUsers: number;
	avgMessagesPerDay: number;
}

export interface UserSummary {
	userId: string;
	username: string;
	messageCount: number;
	channelsActive: number;
	avgMessageLength: number;
}

export interface MessageTypeCount {
	messageType: string;
	count: number;
	percentage: number;
}

export interface HourlyActivity {
	hour: number;
	messageCount: number;
	uniqueUsers: number;
	avgMessageLength: number;
}

export interface GrowthMetrics {
	dailyGrowthRate: number;
	weeklyGrowthRate: number;
	monthlyGrowthRate: number;
	newUsersPerDay: number;
	retentionRate: number;
}

export interface PeakActivityAnalysis {
	dailyPeaks: DailyPeak[];
	weeklyPatterns: WeeklyPattern[];
	activityTrends: ActivityTrend[];
	anomalies: ActivityAnomaly[];
}

export interface DailyPeak {
	date: string;
	peakHour: number;
	peakMessageCount: number;
	peakUserCount: number;
	activityScore: number;
}

export interface WeeklyPattern {
	dayOfWeek: number;
	avgMessageCount: number;
	avgUserCount: number;
	peakHours: number[];
}

export interface ActivityTrend {
	period: string;
	messageGrowth: number;
	userGrowth: number;
	engagementChange: number;
}

export interface ActivityAnomaly {
	timestamp: string;
	anomalyType: string;
	severity: number;
	description: string;
}

export interface EngagementMetrics {
	userEngagement: UserEngagement[];
	channelGrowth: ChannelGrowth[];
	retentionMetrics: RetentionMetrics;
	activityDistribution: ActivityDistribution[];
}

export interface UserEngagement {
	userId: string;
	username: string;
	engagementScore: number;
	messageCount: number;
	activeDays: number;
	avgMessageLength: number;
	activeHoursCount: number;
}

export interface ChannelGrowth {
	channelId: string;
	channelName: string;
	growthRate: number;
	newUsers: number;
	messageGrowth: number;
	retentionRate: number;
}

export interface RetentionMetrics {
	dailyRetention: number;
	weeklyRetention: number;
	monthlyRetention: number;
	avgSessionLength: number;
}

export interface ActivityDistribution {
	timeSlot: string;
	messageCount: number;
	userCount: number;
	percentage: number;
}
