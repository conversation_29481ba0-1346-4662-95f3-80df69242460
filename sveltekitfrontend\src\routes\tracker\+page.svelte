<script lang="ts">
	import type { TrackedUser, FullLogMessage } from '$lib/types';
	import { useAdvancedSearchQuery, useUserAnalyticsQuery } from '$lib/api';
	import LogLine from '$lib/components/LogLine.svelte';
	import SearchResults from '$lib/components/SearchResults.svelte';
	import BarChart from '$lib/components/charts/BarChart.svelte';
	import LineChart from '$lib/components/charts/LineChart.svelte';
	import { subDays, format } from 'date-fns';

	let trackedUsers = $state<TrackedUser[]>([]);
	let newUser = $state('');
	let selectedUser = $state<TrackedUser | null>(null);
	let timeRange = $state(7); // days
	let selectedTab = $state<'overview' | 'activity' | 'messages'>('overview');

	const to = $derived(new Date());
	const from = $derived(subDays(to, timeRange));

	// Get analytics for all tracked users
	const userAnalyticsQuery = $derived(
		useUserAnalyticsQuery(trackedUsers.map(u => u.username), from, to)
	);

	// Get recent messages for tracked users
	const userSearchParams = $derived({
		users: trackedUsers.map((u) => u.username),
		from: from.toISOString(),
		to: to.toISOString()
	});
	const userMessagesQuery = $derived(useAdvancedSearchQuery(userSearchParams, 'global', undefined));

	// Transform data for selected user
	const selectedUserData = $derived(() => {
		if (!selectedUser || !$userAnalyticsQuery.data) return null;

		const userData = $userAnalyticsQuery.data.topUsers?.find(
			u => u.username === selectedUser.username
		);

		const userMessages = $userMessagesQuery.data?.filter(
			msg => msg.username === selectedUser.username || msg.displayName === selectedUser.username
		) || [];

		// Group messages by channel
		const messagesByChannel = userMessages.reduce((acc, msg) => {
			if (!acc[msg.channel]) {
				acc[msg.channel] = [];
			}
			acc[msg.channel].push(msg);
			return acc;
		}, {} as Record<string, FullLogMessage[]>);

		// Calculate channel stats
		const channelStats = Object.entries(messagesByChannel).map(([channel, messages]) => ({
			channel,
			messageCount: messages.length,
			percentage: (messages.length / userMessages.length) * 100,
			latestMessage: messages.sort((a, b) =>
				new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
			)[0]
		})).sort((a, b) => b.messageCount - a.messageCount);

		return {
			userData,
			userMessages,
			messagesByChannel,
			channelStats,
			totalMessages: userMessages.length,
			totalChannels: channelStats.length
		};
	});

	function addUser(event: SubmitEvent) {
		event.preventDefault();
		if (newUser && !trackedUsers.find((u) => u.username === newUser)) {
			trackedUsers = [
				...trackedUsers,
				{
					userId: newUser,
					username: newUser,
					addedAt: new Date()
				}
			];
			newUser = '';
		}
	}

	function removeUser(username: string) {
		trackedUsers = trackedUsers.filter((u) => u.username !== username);
		if (selectedUser?.username === username) {
			selectedUser = null;
		}
	}

	function selectUser(user: TrackedUser) {
		selectedUser = selectedUser?.username === user.username ? null : user;
	}

	// Get user activity summary
	function getUserSummary(username: string) {
		if (!$userAnalyticsQuery.data) return null;

		const userData = $userAnalyticsQuery.data.topUsers?.find(u => u.username === username);
		const userMessages = $userMessagesQuery.data?.filter(
			msg => msg.username === username || msg.displayName === username
		) || [];

		return {
			messageCount: userData?.messageCount || 0,
			channelsActive: userData?.channelsActive || 0,
			recentMessages: userMessages.length,
			avgMessageLength: userData?.avgMessageLength || 0
		};
	}
</script>

<div class="container">
	<h1>User Tracker</h1>
	<p>Monitor activity for specific users across all channels.</p>

	<form class="add-form" onsubmit={addUser}>
		<input type="search" placeholder="Add username to track..." bind:value={newUser} />
		<button type="submit">Track</button>
	</form>

	<div class="tracked-list">
		{#each trackedUsers as user (user.username)}
			<div class="tracked-user">
				<span>{user.username}</span>
				<button class="remove" onclick={() => removeUser(user.username)}>×</button>
			</div>
		{/each}
	</div>

	<div class="results">
		{#if trackedUsers.length > 0}
			{#if userSearchQuery.isPending}
				<p>Loading activity...</p>
			{:else if userSearchQuery.isError}
				<p class="error">Error: {userSearchQuery.error.message}</p>
			{:else if userSearchQuery.data}
				<p>Found {userSearchQuery.data.length} messages.</p>
				<div class="log-list">
					{#each userSearchQuery.data as message (message.id)}
						<LogLine {message} />
					{/each}
				</div>
			{/if}
		{/if}
	</div>
</div>

<style>
	.container {
		padding: 1rem;
		background: var(--bg-bright);
		border-radius: 8px;
	}
	.add-form {
		display: flex;
		gap: 0.5rem;
		margin-bottom: 1rem;
	}
	.add-form input {
		flex-grow: 1;
	}
	.tracked-list {
		display: flex;
		flex-wrap: wrap;
		gap: 0.5rem;
		margin-bottom: 1rem;
	}
	.tracked-user {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.25rem 0.75rem;
		background: var(--bg-dark);
		border-radius: 99px;
	}
	.remove {
		background: none;
		border: none;
		color: var(--text-dark);
		cursor: pointer;
		font-size: 1.2rem;
		padding: 0;
		line-height: 1;
	}
	.remove:hover {
		color: var(--danger);
	}
	.log-list {
		max-height: 60vh;
		overflow-y: auto;
		background: var(--bg-dark);
		padding: 0.5rem;
		border-radius: 4px;
	}
	.error {
		color: var(--danger);
	}
</style>