import { createQuery } from '@tanstack/svelte-query';
import { parseISO } from 'date-fns';
import { parseEmotes } from '$lib/services/emotes';
import type {
	AdvancedSearchParams,
	AvailableLogs,
	Channel,
	LogMessage,
	UserLogResponse,
	FullLogMessage,
	GlobalAnalytics,
	BasicLogMessage,
	PeakActivityAnalysis,
	EngagementMetrics
} from '$lib/types';
import { settings } from './store';
import { get } from 'svelte/store';
import { apiUrl } from './config';

function isUserId(value: string) {
	return value.startsWith('id:');
}
function getUserId(value: string) {
	return value.replace('id:', '');
}

export const queryKeys = {
	channels: ['channels'],
	availableLogs: (channel: string | null, user: string | null) => ['availableLogs', channel, user],
	logs: (channel: string, user: string, year: string, month: string) => [
		'logs',
		channel,
		user,
		year,
		month
	],
	advancedSearch: (params: AdvancedSearchParams, scope: 'channel' | 'global', channel?: string) => [
		'advancedSearch',
		scope,
		channel,
		params
	],
	globalAnalytics: (from: Date, to: Date) => ['globalAnalytics', from, to]
};

async function apiFetch(path: string, options?: RequestInit) {
	const response = await fetch(apiUrl(path), options);
	if (!response.ok) {
		const errorText = await response.text();
		throw new Error(errorText || `HTTP error! status: ${response.status}`);
	}
	const text = await response.text();
	return text ? JSON.parse(text) : null;
}

export function useChannelsQuery() {
	return createQuery({
		queryKey: queryKeys.channels,
		queryFn: async (): Promise<Channel[]> => {
			const data = await apiFetch('/channels');
			return data?.channels ?? [];
		}
	});
}

export function useAvailableLogsQuery(channel: string | null, user: string | null) {
	return createQuery({
		queryKey: queryKeys.availableLogs(channel, user),
		queryFn: async (): Promise<AvailableLogs> => {
			if (!channel) return [];
			let url = `/list?`;
			const params = new URLSearchParams();
			if (user) {
				const userIsId = isUserId(user);
				params.append(`user${userIsId ? 'id' : ''}`, userIsId ? getUserId(user) : user);
			}
			const channelIsId = isUserId(channel);
			params.append(
				`channel${channelIsId ? 'id' : ''}`,
				channelIsId ? getUserId(channel) : channel
			);
			url += params.toString();
			const data = await apiFetch(url);
			return data?.availableLogs ?? [];
		},
		enabled: !!channel
	});
}

export function useLogsQuery(
	channel: string | null,
	user: string | null,
	year: string,
	month: string
) {
	const $settings = get(settings);
	return createQuery({
		queryKey: queryKeys.logs(channel ?? '', user ?? '', year, month),
		queryFn: async (): Promise<LogMessage[]> => {
			if (!channel || !user) return [];

			const channelIsId = isUserId(channel);
			const userIsId = isUserId(user);
			const channelParam = channelIsId ? getUserId(channel) : channel;
			const userParam = userIsId ? getUserId(user) : user;

			const path = `/channel${channelIsId ? 'id' : ''}/${channelParam}/user${
				userIsId ? 'id' : ''
			}/${userParam}/${year}/${month}`;
			const params = new URLSearchParams({ jsonBasic: '1' });
			if (!$settings.newOnBottom) {
				params.append('reverse', '1');
			}

			const data: UserLogResponse | null = await apiFetch(`${path}?${params.toString()}`);
			if (!data || !data.messages) return [];

			return data.messages.map((msg) => ({
				...msg,
				timestamp: parseISO(msg.timestamp),
				emotes: parseEmotes(msg.text, msg.tags['emotes'])
			}));
		},
		enabled: !!channel && !!user
	});
}

export function useAdvancedSearchQuery(
	params: AdvancedSearchParams,
	scope: 'channel' | 'global',
	channel: string | undefined
) {
	const $settings = get(settings);
	const enabled =
		(scope === 'global' || !!channel) &&
		(!!params.q || (!!params.users && params.users.length > 0));

	return createQuery({
		queryKey: queryKeys.advancedSearch(params, scope, channel),
		queryFn: async (): Promise<FullLogMessage[]> => {
			if (!enabled) return [];

			const query = new URLSearchParams({ json: '1' });
			if (!$settings.newOnBottom) {
				query.append('reverse', '1');
			}

			for (const [key, value] of Object.entries(params)) {
				if (value) {
					if (Array.isArray(value)) {
						if (value.length > 0) query.append(key, value.join(','));
					} else if (typeof value === 'boolean') {
						query.append(key, '1');
					} else if (value instanceof Date) {
						query.append(key, value.toISOString());
					} else {
						query.append(key, String(value));
					}
				}
			}

			let path = '/advanced-search';
			if (scope === 'channel' && channel) {
				const channelIsId = isUserId(channel);
				path = `/channel${channelIsId ? 'id' : ''}/${
					channelIsId ? getUserId(channel) : channel
				}${path}`;
			}

			// The API will return messages with string timestamps.
			const data: {
				messages: (Omit<FullLogMessage, 'timestamp' | 'emotes'> & { timestamp: string })[];
			} | null = await apiFetch(`${path}?${query.toString()}`);

			if (!data || !data.messages) return [];

			// We map to the FullLogMessage type with Date objects.
			return data.messages.map((msg) => ({
				...msg,
				timestamp: parseISO(msg.timestamp),
				emotes: parseEmotes(msg.text, msg.tags['emotes'])
			}));
		},
		enabled: enabled
	});
}

export function useGlobalAnalyticsQuery(from: Date, to: Date) {
	return createQuery({
		queryKey: queryKeys.globalAnalytics(from, to),
		queryFn: async (): Promise<GlobalAnalytics> => {
			const params = new URLSearchParams({ from: from.toISOString(), to: to.toISOString() });
			return apiFetch(`/analytics?${params.toString()}`);
		}
	});
}

export function usePeakActivityQuery(from: Date, to: Date) {
	return createQuery({
		queryKey: ['peakActivity', from, to],
		queryFn: async (): Promise<PeakActivityAnalysis> => {
			const params = new URLSearchParams({ from: from.toISOString(), to: to.toISOString() });
			return apiFetch(`/analytics/peak-activity?${params.toString()}`);
		}
	});
}

export function useEngagementMetricsQuery(from: Date, to: Date) {
	return createQuery({
		queryKey: ['engagementMetrics', from, to],
		queryFn: async (): Promise<EngagementMetrics> => {
			const params = new URLSearchParams({ from: from.toISOString(), to: to.toISOString() });
			return apiFetch(`/analytics/engagement-metrics?${params.toString()}`);
		}
	});
}

export function useUserAnalyticsQuery(users: string[], from: Date, to: Date) {
	return createQuery({
		queryKey: ['userAnalytics', users, from, to],
		queryFn: async (): Promise<GlobalAnalytics> => {
			const params = new URLSearchParams({
				from: from.toISOString(),
				to: to.toISOString(),
				users: users.join(',')
			});
			return apiFetch(`/analytics?${params.toString()}`);
		},
		enabled: users.length > 0
	});
}

export function useUserNameHistoryQuery(userId: string) {
	return createQuery({
		queryKey: ['userNameHistory', userId],
		queryFn: async (): Promise<{
			names: Array<{ name: string; firstSeen: string; lastSeen: string }>;
		}> => {
			return apiFetch(`/namehistory/${userId}`);
		},
		enabled: !!userId
	});
}
