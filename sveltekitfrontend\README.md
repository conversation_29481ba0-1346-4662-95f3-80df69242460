# SvelteKit Frontend for Rustlog

A modern SvelteKit frontend for the Rustlog chat logging backend.

## Development Setup

1. **Install dependencies:**

   ```bash
   npm install
   ```

2. **Start the development server:**

   ```bash
   npm run dev
   ```

   This will start the SvelteKit dev server with API proxy to your Rust backend at `localhost:8025`.

3. **Make sure your Rust backend is running:**
   ```bash
   # In the main project directory
   cargo run
   ```

## Building for Production

1. **Build the static files:**

   ```bash
   npm run build:prod
   ```

   This creates static files in the `build/` directory.

## Deployment with Nginx

1. **Build the frontend:**

   ```bash
   npm run build:prod
   ```

2. **Copy the nginx configuration:**

   ```bash
   sudo cp nginx.conf.example /etc/nginx/sites-available/rustlog-frontend
   ```

3. **Edit the configuration:**
   - Update the `server_name` to your domain
   - Update the `root` path to point to your `build/` directory
   - Adjust the backend proxy URL if needed

4. **Enable the site:**
   ```bash
   sudo ln -s /etc/nginx/sites-available/rustlog-frontend /etc/nginx/sites-enabled/
   sudo nginx -t  # Test configuration
   sudo systemctl reload nginx
   ```

## API Configuration

The frontend automatically configures API endpoints based on the environment:

- **Development**: Uses `/api` proxy to `localhost:8025`
- **Production**: Makes direct requests to `http://localhost:8025`

You can override the API base URL by setting the `VITE_API_BASE_URL` environment variable.

## Environment Variables

- `VITE_API_BASE_URL`: Override the default API base URL

## Architecture

- **Frontend**: SvelteKit with static adapter
- **Backend**: Rust server on port 8025
- **Deployment**: Nginx serves static files and proxies API requests
