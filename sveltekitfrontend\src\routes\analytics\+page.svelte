<script lang="ts">
	import { useGlobalAnalyticsQuery, usePeakActivityQuery, useEngagementMetricsQuery } from '$lib/api';
	import { subDays, startOfDay, format } from 'date-fns';
	import Bar<PERSON>hart from '$lib/components/charts/BarChart.svelte';
	import DoughnutChart from '$lib/components/charts/DoughnutChart.svelte';
	import Line<PERSON>hart from '$lib/components/charts/LineChart.svelte';
	import Heatmap<PERSON><PERSON> from '$lib/components/charts/HeatmapChart.svelte';

	let dateRange = $state(30);
	let selectedTab = $state<'overview' | 'activity' | 'engagement'>('overview');

	const to = $derived(startOfDay(new Date()));
	const from = $derived(subDays(to, dateRange));

	const analyticsQuery = $derived(useGlobalAnalyticsQuery(from, to));
	const peakActivityQuery = $derived(usePeakActivityQuery(from, to));
	const engagementQuery = $derived(useEngagementMetricsQuery(from, to));

	// Transform data for charts
	const topUsersChartData = $derived(() => {
		if (!$analyticsQuery.data?.topUsers) return [];
		return $analyticsQuery.data.topUsers.slice(0, 10).map(user => ({
			x: user.username,
			y: user.messageCount,
			label: user.username
		}));
	});

	const topChannelsChartData = $derived(() => {
		if (!$analyticsQuery.data?.topChannels) return [];
		return $analyticsQuery.data.topChannels.slice(0, 10).map(channel => ({
			x: channel.channelName,
			y: channel.messageCount,
			label: channel.channelName
		}));
	});

	const activityTimelineData = $derived(() => {
		if (!$peakActivityQuery.data?.dailyPeaks) return [];
		return $peakActivityQuery.data.dailyPeaks.map(peak => ({
			x: peak.date,
			y: peak.peakMessageCount,
			label: format(new Date(peak.date), 'MMM dd')
		}));
	});
</script>

<div class="container">
	<div class="dashboard-header">
		<div class="header-content">
			<div class="title-section">
				<h1>📊 Analytics Dashboard</h1>
				<p>Comprehensive insights into chat activity and user engagement</p>
			</div>
			<div class="controls">
				<select bind:value={dateRange} class="date-range-select">
					<option value={7}>Last 7 days</option>
					<option value={30}>Last 30 days</option>
					<option value={90}>Last 90 days</option>
					<option value={365}>Last year</option>
				</select>
			</div>
		</div>
	</div>

	<!-- Tab Navigation -->
	<div class="tab-navigation">
		<button
			class="tab"
			class:active={selectedTab === 'overview'}
			onclick={() => (selectedTab = 'overview')}
		>
			📈 Overview
		</button>
		<button
			class="tab"
			class:active={selectedTab === 'activity'}
			onclick={() => (selectedTab = 'activity')}
		>
			⚡ Activity Patterns
		</button>
		<button
			class="tab"
			class:active={selectedTab === 'engagement'}
			onclick={() => (selectedTab = 'engagement')}
		>
			👥 User Engagement
		</button>
	</div>

	{#if $analyticsQuery.isPending}
		<div class="loading">
			<div class="spinner"></div>
			<p>Loading analytics data...</p>
		</div>
	{:else if $analyticsQuery.isError}
		<div class="error-container">
			<p class="error">Error loading analytics: {$analyticsQuery.error.message}</p>
		</div>
	{:else if $analyticsQuery.data}
		{@const data = $analyticsQuery.data}

		<!-- Overview Tab -->
		{#if selectedTab === 'overview'}
			<!-- Key Metrics -->
			<div class="metrics-grid">
				<div class="metric-card primary">
					<div class="metric-icon">💬</div>
					<div class="metric-content">
						<h3>Total Messages</h3>
						<p class="metric-value">{data.totalMessages.toLocaleString()}</p>
						<p class="metric-subtitle">
							{(data.totalMessages / dateRange).toFixed(0)} per day average
						</p>
					</div>
				</div>
				<div class="metric-card secondary">
					<div class="metric-icon">👤</div>
					<div class="metric-content">
						<h3>Active Users</h3>
						<p class="metric-value">{data.totalUsers.toLocaleString()}</p>
						<p class="metric-subtitle">
							{data.growthMetrics.newUsersPerDay.toFixed(1)} new per day
						</p>
					</div>
				</div>
				<div class="metric-card accent">
					<div class="metric-icon">📺</div>
					<div class="metric-content">
						<h3>Active Channels</h3>
						<p class="metric-value">{data.totalChannels.toLocaleString()}</p>
						<p class="metric-subtitle">
							{(data.totalMessages / data.totalChannels).toFixed(0)} messages per channel
						</p>
					</div>
				</div>
				<div class="metric-card success">
					<div class="metric-icon">📊</div>
					<div class="metric-content">
						<h3>Engagement Rate</h3>
						<p class="metric-value">{(data.totalMessages / data.totalUsers).toFixed(1)}</p>
						<p class="metric-subtitle">messages per user</p>
					</div>
				</div>
			</div>

			<!-- Charts Grid -->
			<div class="charts-grid">
				<div class="chart-card full-width">
					{#if data.activityHeatmap?.length > 0}
						<HeatmapChart
							data={data.activityHeatmap}
							title="24-Hour Activity Heatmap"
							height={200}
						/>
					{/if}
				</div>

				<div class="chart-card">
					{#if data.messageDistribution?.length > 0}
						<DoughnutChart data={data.messageDistribution} title="Message Type Distribution" />
					{/if}
				</div>

				<div class="chart-card">
					{#if topChannelsChartData.length > 0}
						<BarChart
							data={topChannelsChartData}
							title="Top Channels by Activity"
							xLabel="Channel"
							yLabel="Messages"
						/>
					{/if}
				</div>
			</div>
		{/if}

		<!-- Activity Patterns Tab -->
		{#if selectedTab === 'activity'}
			<div class="charts-grid">
				{#if $peakActivityQuery.data}
					<div class="chart-card full-width">
						{#if activityTimelineData.length > 0}
							<LineChart
								data={activityTimelineData}
								title="Daily Peak Activity Timeline"
								xLabel="Date"
								yLabel="Peak Messages"
								color="var(--theme)"
							/>
						{/if}
					</div>

					<div class="chart-card">
						{#if $peakActivityQuery.data.weeklyPatterns?.length > 0}
							<BarChart
								data={$peakActivityQuery.data.weeklyPatterns.map(pattern => ({
									x: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][pattern.dayOfWeek],
									y: pattern.avgMessageCount,
									label: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][pattern.dayOfWeek]
								}))}
								title="Weekly Activity Patterns"
								xLabel="Day of Week"
								yLabel="Average Messages"
							/>
						{/if}
					</div>

					<div class="chart-card">
						{#if $peakActivityQuery.data.activityTrends?.length > 0}
							<LineChart
								data={$peakActivityQuery.data.activityTrends.map(trend => ({
									x: trend.period,
									y: trend.messageGrowth,
									label: trend.period
								}))}
								title="Message Growth Trends"
								xLabel="Period"
								yLabel="Growth %"
								color="var(--theme2)"
							/>
						{/if}
					</div>
				{:else if $peakActivityQuery.isPending}
					<div class="loading">
						<div class="spinner"></div>
						<p>Loading activity patterns...</p>
					</div>
				{:else if $peakActivityQuery.isError}
					<div class="error-container">
						<p class="error">Error loading activity data: {$peakActivityQuery.error.message}</p>
					</div>
				{/if}
			</div>
		{/if}

		<!-- User Engagement Tab -->
		{#if selectedTab === 'engagement'}
			<div class="charts-grid">
				{#if $engagementQuery.data}
					<div class="chart-card">
						{#if topUsersChartData.length > 0}
							<BarChart
								data={topUsersChartData}
								title="Top Users by Activity"
								xLabel="User"
								yLabel="Messages"
							/>
						{/if}
					</div>

					<div class="chart-card">
						{#if $engagementQuery.data.userEngagement?.length > 0}
							<LineChart
								data={$engagementQuery.data.userEngagement.slice(0, 10).map(user => ({
									x: user.username,
									y: user.engagementScore,
									label: user.username
								}))}
								title="User Engagement Scores"
								xLabel="User"
								yLabel="Engagement Score"
								color="var(--theme)"
							/>
						{/if}
					</div>

					<div class="chart-card full-width">
						{#if $engagementQuery.data.channelGrowth?.length > 0}
							<BarChart
								data={$engagementQuery.data.channelGrowth.slice(0, 15).map(channel => ({
									x: channel.channelName,
									y: channel.growthRate,
									label: channel.channelName
								}))}
								title="Channel Growth Rates"
								xLabel="Channel"
								yLabel="Growth Rate %"
							/>
						{/if}
					</div>
				{:else if $engagementQuery.isPending}
					<div class="loading">
						<div class="spinner"></div>
						<p>Loading engagement metrics...</p>
					</div>
				{:else if $engagementQuery.isError}
					<div class="error-container">
						<p class="error">Error loading engagement data: {$engagementQuery.error.message}</p>
					</div>
				{/if}
			</div>
		{/if}
	{/if}
</div>

<style>
	.container {
		padding: 1rem;
		max-width: 1400px;
		margin: 0 auto;
	}

	.dashboard-header {
		background: linear-gradient(135deg, var(--theme) 0%, var(--theme-bright) 100%);
		color: white;
		padding: 2rem;
		border-radius: 12px;
		margin-bottom: 1.5rem;
	}

	.header-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		gap: 1rem;
	}

	.title-section h1 {
		margin: 0 0 0.5rem 0;
		font-size: 2.5rem;
		font-weight: 700;
	}

	.title-section p {
		margin: 0;
		opacity: 0.9;
		font-size: 1.1rem;
	}

	.controls {
		display: flex;
		gap: 1rem;
		align-items: center;
	}

	.date-range-select {
		padding: 0.5rem 1rem;
		border-radius: 8px;
		border: 2px solid rgba(255, 255, 255, 0.3);
		background: rgba(255, 255, 255, 0.1);
		color: white;
		font-size: 1rem;
	}

	.date-range-select option {
		background: var(--bg-dark);
		color: var(--text);
	}

	.tab-navigation {
		display: flex;
		gap: 0.5rem;
		margin-bottom: 1.5rem;
		background: var(--bg-bright);
		padding: 0.5rem;
		border-radius: 12px;
		border: 1px solid var(--border-color);
	}

	.tab {
		flex: 1;
		padding: 0.75rem 1rem;
		border: none;
		background: transparent;
		color: var(--text-dark);
		border-radius: 8px;
		cursor: pointer;
		transition: all 0.2s;
		font-size: 1rem;
		font-weight: 500;
	}

	.tab:hover {
		background: var(--bg-dark);
		color: var(--text);
	}

	.tab.active {
		background: var(--theme2);
		color: white;
	}

	.loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 3rem;
		gap: 1rem;
	}

	.spinner {
		width: 3rem;
		height: 3rem;
		border: 4px solid var(--bg-bright);
		border-top: 4px solid var(--theme2);
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.error-container {
		padding: 2rem;
		text-align: center;
	}

	.error {
		color: var(--danger);
		background: var(--bg-bright);
		padding: 1rem;
		border-radius: 8px;
		border-left: 4px solid var(--danger);
	}

	.metrics-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: 1.5rem;
		margin-bottom: 2rem;
	}

	.metric-card {
		display: flex;
		align-items: center;
		gap: 1rem;
		padding: 1.5rem;
		border-radius: 12px;
		border: 1px solid var(--border-color);
		transition: transform 0.2s, box-shadow 0.2s;
	}

	.metric-card:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}

	.metric-card.primary {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		color: white;
	}

	.metric-card.secondary {
		background: linear-gradient(135deg, var(--theme) 0%, var(--theme-bright) 100%);
		color: white;
	}

	.metric-card.accent {
		background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
		color: white;
	}

	.metric-card.success {
		background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
		color: white;
	}

	.metric-icon {
		font-size: 2.5rem;
		opacity: 0.9;
	}

	.metric-content h3 {
		margin: 0 0 0.5rem 0;
		font-size: 1rem;
		font-weight: 600;
		opacity: 0.9;
	}

	.metric-value {
		margin: 0 0 0.25rem 0;
		font-size: 2rem;
		font-weight: 700;
		line-height: 1;
	}

	.metric-subtitle {
		margin: 0;
		font-size: 0.875rem;
		opacity: 0.8;
	}

	.charts-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
		gap: 1.5rem;
	}

	.chart-card {
		background: var(--bg-bright);
		border-radius: 12px;
		border: 1px solid var(--border-color);
		overflow: hidden;
		min-height: 400px;
	}

	.chart-card.full-width {
		grid-column: 1 / -1;
	}
</style>