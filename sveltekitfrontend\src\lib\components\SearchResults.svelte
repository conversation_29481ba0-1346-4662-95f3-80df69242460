<script lang="ts">
	import type { FullLogMessage } from '$lib/types';
	import LogLine from './LogLine.svelte';
	import { createEventDispatcher } from 'svelte';

	interface Props {
		results: FullLogMessage[] | undefined;
		isLoading: boolean;
		error: any;
		searchQuery?: string;
		isGlobalSearch?: boolean;
	}

	let {
		results,
		isLoading,
		error,
		searchQuery = '',
		isGlobalSearch = false
	}: Props = $props();

	const dispatch = createEventDispatcher<{
		clearSearch: void;
		export: void;
	}>();

	let currentPage = $state(1);
	let sortBy = $state<'timestamp' | 'channel' | 'user'>('timestamp');
	let sortOrder = $state<'asc' | 'desc'>('desc');
	let selectedChannels = $state<string[]>([]);
	let selectedUsers = $state<string[]>([]);
	let showFilters = $state(false);

	const ITEMS_PER_PAGE = 50;

	// Extract unique channels and users from results
	const availableChannels = $derived(() => {
		if (!results) return [];
		return [...new Set(results.map((msg) => msg.channel))].sort();
	});

	const availableUsers = $derived(() => {
		if (!results) return [];
		return [...new Set(results.map((msg) => msg.username || msg.displayName))].sort();
	});

	// Filter results based on selected channels and users
	const filteredResults = $derived(() => {
		if (!results) return [];

		let filtered = results;

		if (selectedChannels.length > 0) {
			filtered = filtered.filter((msg) => selectedChannels.includes(msg.channel));
		}

		if (selectedUsers.length > 0) {
			filtered = filtered.filter(
				(msg) =>
					selectedUsers.includes(msg.username) || selectedUsers.includes(msg.displayName)
			);
		}

		return filtered;
	});

	// Sort filtered results
	const sortedResults = $derived(() => {
		return [...filteredResults].sort((a, b) => {
			if (sortBy === 'timestamp') {
				const timeA = new Date(a.timestamp).getTime();
				const timeB = new Date(b.timestamp).getTime();
				return sortOrder === 'asc' ? timeA - timeB : timeB - timeA;
			} else if (sortBy === 'channel') {
				const channelA = a.channel.toLowerCase();
				const channelB = b.channel.toLowerCase();
				return sortOrder === 'asc'
					? channelA.localeCompare(channelB)
					: channelB.localeCompare(channelA);
			} else if (sortBy === 'user') {
				const userA = (a.username || a.displayName).toLowerCase();
				const userB = (b.username || b.displayName).toLowerCase();
				return sortOrder === 'asc' ? userA.localeCompare(userB) : userB.localeCompare(userA);
			}
			return 0;
		});
	});

	// Paginate results
	const paginatedResults = $derived(() => {
		const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
		const endIndex = startIndex + ITEMS_PER_PAGE;
		return sortedResults.slice(startIndex, endIndex);
	});

	const totalPages = $derived(() => Math.ceil(sortedResults.length / ITEMS_PER_PAGE));

	// Channel and user statistics
	const channelStats = $derived(() => {
		const stats = new Map<string, number>();
		filteredResults.forEach((msg) => {
			stats.set(msg.channel, (stats.get(msg.channel) || 0) + 1);
		});
		return Array.from(stats.entries())
			.map(([channel, count]) => ({ channel, count }))
			.sort((a, b) => b.count - a.count);
	});

	const userStats = $derived(() => {
		const stats = new Map<string, number>();
		filteredResults.forEach((msg) => {
			const user = msg.username || msg.displayName;
			stats.set(user, (stats.get(user) || 0) + 1);
		});
		return Array.from(stats.entries())
			.map(([user, count]) => ({ user, count }))
			.sort((a, b) => b.count - a.count);
	});

	function handleExport() {
		if (results) {
			const dataStr = JSON.stringify(results, null, 2);
			const dataBlob = new Blob([dataStr], { type: 'application/json' });
			const url = URL.createObjectURL(dataBlob);
			const link = document.createElement('a');
			link.href = url;
			link.download = `search-results-${new Date().toISOString().split('T')[0]}.json`;
			link.click();
			URL.revokeObjectURL(url);
		}
	}

	function toggleChannelFilter(channel: string) {
		if (selectedChannels.includes(channel)) {
			selectedChannels = selectedChannels.filter((c) => c !== channel);
		} else {
			selectedChannels = [...selectedChannels, channel];
		}
		currentPage = 1; // Reset to first page when filtering
	}

	function toggleUserFilter(user: string) {
		if (selectedUsers.includes(user)) {
			selectedUsers = selectedUsers.filter((u) => u !== user);
		} else {
			selectedUsers = [...selectedUsers, user];
		}
		currentPage = 1; // Reset to first page when filtering
	}

	function clearAllFilters() {
		selectedChannels = [];
		selectedUsers = [];
		currentPage = 1;
	}

	// Reset page when results change
	$effect(() => {
		if (results) {
			currentPage = 1;
		}
	});
</script>

{#if isLoading}
	<div class="loading">
		<div class="spinner"></div>
		<p>Searching...</p>
	</div>
{:else if error}
	<div class="error-container">
		<div class="error">Search failed: {error.message || 'Unknown error'}</div>
	</div>
{:else if !results || results.length === 0}
	<div class="no-results">
		<p>No results found for "{searchQuery}"</p>
	</div>
{:else}
	<div class="search-results">
		<!-- Results Header -->
		<div class="results-header">
			<div class="results-stats">
				<h3>Search Results</h3>
				<div class="stats-chips">
					<span class="chip primary">{filteredResults.length} results</span>
					{#if filteredResults.length !== results.length}
						<span class="chip secondary">{results.length} total</span>
					{/if}
					{#if isGlobalSearch}
						<span class="chip">{channelStats.length} channels</span>
						<span class="chip">{userStats.length} users</span>
					{/if}
				</div>
			</div>

			<div class="results-controls">
				<select bind:value={sortBy}>
					<option value="timestamp">Sort by Time</option>
					{#if isGlobalSearch}
						<option value="channel">Sort by Channel</option>
						<option value="user">Sort by User</option>
					{/if}
				</select>

				<button
					class="sort-order"
					onclick={() => (sortOrder = sortOrder === 'asc' ? 'desc' : 'asc')}
					title={`Sort ${sortOrder === 'asc' ? 'descending' : 'ascending'}`}
				>
					{sortOrder === 'asc' ? '↑' : '↓'}
				</button>

				{#if isGlobalSearch}
					<button
						class="filter-toggle"
						class:active={showFilters}
						onclick={() => (showFilters = !showFilters)}
					>
						Filters
					</button>
				{/if}

				<button onclick={handleExport}>Export</button>
				<button onclick={() => dispatch('clearSearch')}>Clear</button>
			</div>
		</div>

		<!-- Filters Panel -->
		{#if isGlobalSearch && showFilters}
			<div class="filters-panel">
				<h4>Filters & Analytics</h4>

				<div class="filter-section">
					<h5>Top Channels</h5>
					<div class="filter-chips">
						{#each channelStats.slice(0, 8) as { channel, count }}
							<button
								class="filter-chip"
								class:active={selectedChannels.includes(channel)}
								onclick={() => toggleChannelFilter(channel)}
							>
								#{channel} ({count})
							</button>
						{/each}
					</div>
				</div>

				<div class="filter-section">
					<h5>Top Users</h5>
					<div class="filter-chips">
						{#each userStats.slice(0, 8) as { user, count }}
							<button
								class="filter-chip"
								class:active={selectedUsers.includes(user)}
								onclick={() => toggleUserFilter(user)}
							>
								{user} ({count})
							</button>
						{/each}
					</div>
				</div>

				{#if selectedChannels.length > 0 || selectedUsers.length > 0}
					<button class="clear-filters" onclick={clearAllFilters}>Clear All Filters</button>
				{/if}
			</div>
		{/if}

		<!-- Results List -->
		<div class="results-list">
			{#each paginatedResults as message, index (message.id || index)}
				<div class="result-item">
					{#if isGlobalSearch}
						<div class="message-meta">
							<span class="channel-chip">#{message.channel}</span>
							<span class="user-chip">{message.username || message.displayName}</span>
							<span class="timestamp">{new Date(message.timestamp).toLocaleString()}</span>
						</div>
					{/if}
					<LogLine {message} />
				</div>
			{/each}
		</div>

		<!-- Pagination -->
		{#if totalPages > 1}
			<div class="pagination">
				<button disabled={currentPage === 1} onclick={() => (currentPage = 1)}>First</button>
				<button disabled={currentPage === 1} onclick={() => currentPage--}>Previous</button>
				<span>Page {currentPage} of {totalPages}</span>
				<button disabled={currentPage === totalPages} onclick={() => currentPage++}>Next</button>
				<button disabled={currentPage === totalPages} onclick={() => (currentPage = totalPages)}>
					Last
				</button>
			</div>
		{/if}
	</div>
{/if}

<style>
	.loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 2rem;
		gap: 1rem;
	}

	.spinner {
		width: 2rem;
		height: 2rem;
		border: 3px solid var(--bg-bright);
		border-top: 3px solid var(--theme2);
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	.error-container,
	.no-results {
		padding: 1rem;
		text-align: center;
	}

	.error {
		color: var(--danger);
		background: var(--bg-bright);
		padding: 1rem;
		border-radius: 8px;
		border-left: 4px solid var(--danger);
	}

	.search-results {
		background: var(--bg-bright);
		border-radius: 8px;
		overflow: hidden;
	}

	.results-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		border-bottom: 1px solid var(--border-color);
		flex-wrap: wrap;
		gap: 1rem;
	}

	.results-stats {
		display: flex;
		align-items: center;
		gap: 1rem;
		flex-wrap: wrap;
	}

	.results-stats h3 {
		margin: 0;
		color: var(--theme2);
	}

	.stats-chips {
		display: flex;
		gap: 0.5rem;
		flex-wrap: wrap;
	}

	.chip {
		padding: 0.25rem 0.75rem;
		background: var(--bg-dark);
		border: 1px solid var(--border-color);
		border-radius: 99px;
		font-size: 0.875rem;
	}

	.chip.primary {
		background: var(--theme2);
		border-color: var(--theme2);
		color: white;
	}

	.chip.secondary {
		background: var(--theme);
		border-color: var(--theme);
		color: white;
	}

	.results-controls {
		display: flex;
		gap: 0.5rem;
		align-items: center;
		flex-wrap: wrap;
	}

	.sort-order {
		width: 2rem;
		height: 2rem;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 1.2rem;
	}

	.filter-toggle.active {
		background: var(--theme2);
		border-color: var(--theme2);
		color: white;
	}

	.filters-panel {
		padding: 1rem;
		background: var(--bg-dark);
		border-bottom: 1px solid var(--border-color);
	}

	.filters-panel h4 {
		margin: 0 0 1rem 0;
		color: var(--theme2);
	}

	.filter-section {
		margin-bottom: 1rem;
	}

	.filter-section h5 {
		margin: 0 0 0.5rem 0;
		color: var(--text-dark);
		font-size: 0.875rem;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.filter-chips {
		display: flex;
		gap: 0.5rem;
		flex-wrap: wrap;
	}

	.filter-chip {
		padding: 0.25rem 0.75rem;
		background: var(--bg-bright);
		border: 1px solid var(--border-color);
		border-radius: 99px;
		font-size: 0.875rem;
		cursor: pointer;
		transition: all 0.2s;
	}

	.filter-chip:hover {
		border-color: var(--theme2);
	}

	.filter-chip.active {
		background: var(--theme2);
		border-color: var(--theme2);
		color: white;
	}

	.clear-filters {
		background: var(--danger);
		border-color: var(--danger);
		color: white;
		margin-top: 0.5rem;
	}

	.results-list {
		max-height: 600px;
		overflow-y: auto;
	}

	.result-item {
		padding: 0.75rem 1rem;
		border-bottom: 1px solid var(--border-color);
	}

	.result-item:last-child {
		border-bottom: none;
	}

	.result-item:hover {
		background: var(--bg-dark);
	}

	.message-meta {
		display: flex;
		gap: 0.5rem;
		margin-bottom: 0.5rem;
		align-items: center;
		flex-wrap: wrap;
	}

	.channel-chip,
	.user-chip {
		padding: 0.125rem 0.5rem;
		border-radius: 4px;
		font-size: 0.75rem;
		font-weight: 500;
	}

	.channel-chip {
		background: var(--theme2);
		color: white;
	}

	.user-chip {
		background: var(--theme);
		color: white;
	}

	.timestamp {
		font-size: 0.75rem;
		color: var(--text-dark);
	}

	.pagination {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 0.5rem;
		padding: 1rem;
		border-top: 1px solid var(--border-color);
	}

	.pagination button:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.pagination span {
		margin: 0 1rem;
		color: var(--text-dark);
	}
</style>
