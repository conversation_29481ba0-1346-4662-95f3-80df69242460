<script lang="ts">
	import { useAdvancedSearchQuery, useChannelsQuery } from '$lib/api';
	import SearchResults from '$lib/components/SearchResults.svelte';
	import type { AdvancedSearchParams } from '$lib/types';
	import { subDays } from 'date-fns';

	let searchQuery = $state('');
	let useAdvancedMode = $state(false);
	let showAdvancedFields = $state(false);
	let advancedParams = $state<AdvancedSearchParams>({});

	const channelsQuery = useChannelsQuery();

	// Convert form data to search params
	const searchParams = $derived(() => {
		if (useAdvancedMode) {
			// Convert string dates to Date objects for the API
			const params = { ...advancedParams };
			if (params.from && typeof params.from === 'string') {
				params.from = new Date(params.from);
			}
			if (params.to && typeof params.to === 'string') {
				params.to = new Date(params.to);
			}
			return {
				...params,
				q: params.q || undefined
			};
		} else {
			return { q: searchQuery || undefined };
		}
	});

	const searchResultsQuery = useAdvancedSearchQuery(searchParams(), 'global', undefined);

	function handleBasicSearch(event: SubmitEvent) {
		event.preventDefault();
		// Search will trigger automatically via reactive statement
	}

	function handleAdvancedSearch(event: SubmitEvent) {
		event.preventDefault();
		// Search will trigger automatically via reactive statement
	}

	function clearSearch() {
		searchQuery = '';
		advancedParams = {};
		useAdvancedMode = false;
		showAdvancedFields = false;
	}

	function addChannelFilter(channel: string) {
		if (!advancedParams.channels) {
			advancedParams.channels = [];
		}
		if (!advancedParams.channels.includes(channel)) {
			advancedParams.channels = [...advancedParams.channels, channel];
		}
	}

	function removeChannelFilter(channel: string) {
		if (advancedParams.channels) {
			advancedParams.channels = advancedParams.channels.filter(c => c !== channel);
		}
	}

	function addUserFilter(user: string) {
		if (!advancedParams.users) {
			advancedParams.users = [];
		}
		if (!advancedParams.users.includes(user)) {
			advancedParams.users = [...advancedParams.users, user];
		}
	}

	function removeUserFilter(user: string) {
		if (advancedParams.users) {
			advancedParams.users = advancedParams.users.filter(u => u !== user);
		}
	}

	// Set default date range when advanced mode is enabled
	$effect(() => {
		if (useAdvancedMode && !advancedParams.from && !advancedParams.to) {
			const now = new Date();
			const thirtyDaysAgo = subDays(now, 30);
			advancedParams = {
				...advancedParams,
				from: thirtyDaysAgo.toISOString().slice(0, 16) as any,
				to: now.toISOString().slice(0, 16) as any
			};
		}
	});
</script>

<div class="container">
	<div class="search-header">
		<div class="header-content">
			<div class="title-section">
				<h1>🌐 Global Search</h1>
				<p>Search across all logged channels and users</p>
			</div>
			<div class="mode-toggle">
				<label class="switch">
					<input type="checkbox" bind:checked={useAdvancedMode} />
					<span class="slider"></span>
					<span class="label">Advanced Mode</span>
				</label>
			</div>
		</div>
	</div>

	{#if useAdvancedMode}
		<div class="info-panel">
			<p>💡 <strong>Tip:</strong> You can search for specific users by adding them to "User Filters" without entering a search query. A 30-day date range will be applied automatically.</p>
		</div>
	{/if}

	<!-- Search Form -->
	<div class="search-section">
		{#if useAdvancedMode}
			<form class="advanced-search-form" onsubmit={handleAdvancedSearch}>
				<div class="search-input-group">
					<input
						type="search"
						placeholder="Enter search terms (optional)..."
						bind:value={advancedParams.q}
						class="search-input"
					/>
					<button
						type="button"
						class="advanced-toggle"
						class:active={showAdvancedFields}
						onclick={() => (showAdvancedFields = !showAdvancedFields)}
					>
						⚙️ Options
					</button>
				</div>

				{#if showAdvancedFields}
					<div class="advanced-fields">
						<div class="field-group">
							<h4>Search Options</h4>
							<div class="checkbox-group">
								<label>
									<input type="checkbox" bind:checked={advancedParams.regex} />
									Regular Expression
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.caseSensitive} />
									Case Sensitive
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.searchDisplayNames} />
									Search Display Names
								</label>
							</div>
						</div>

						<div class="field-group">
							<h4>Date Range</h4>
							<div class="date-inputs">
								<input
									type="datetime-local"
									bind:value={advancedParams.from}
									placeholder="From"
								/>
								<input
									type="datetime-local"
									bind:value={advancedParams.to}
									placeholder="To"
								/>
							</div>
						</div>

						<div class="field-group">
							<h4>Message Length</h4>
							<div class="number-inputs">
								<input
									type="number"
									bind:value={advancedParams.minLength}
									placeholder="Min length"
									min="0"
								/>
								<input
									type="number"
									bind:value={advancedParams.maxLength}
									placeholder="Max length"
									min="0"
								/>
							</div>
						</div>

						<div class="field-group">
							<h4>User Type Filters</h4>
							<div class="checkbox-group">
								<label>
									<input type="checkbox" bind:checked={advancedParams.subscribersOnly} />
									Subscribers Only
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.vipsOnly} />
									VIPs Only
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.moderatorsOnly} />
									Moderators Only
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.firstMessagesOnly} />
									First Messages Only
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.hasEmotes} />
									Has Emotes
								</label>
							</div>
						</div>
					</div>
				{/if}
			</form>
		{:else}
			<form class="basic-search-form" onsubmit={handleBasicSearch}>
				<div class="search-input-group">
					<input
						type="search"
						placeholder="Search across all channels..."
						bind:value={searchQuery}
						class="search-input"
					/>
					<button type="submit">🔍 Search</button>
				</div>
			</form>
		{/if}
	<!-- Channel Filters -->
	{#if useAdvancedMode && $channelsQuery.data}
		<div class="filters-section">
			<h4>Channel Filters</h4>
			<div class="filter-input-group">
				<select onchange={(e) => addChannelFilter((e.target as HTMLSelectElement).value)}>
					<option value="">Add channel...</option>
					{#each $channelsQuery.data as channel}
						<option value={channel.name}>{channel.name}</option>
					{/each}
				</select>
			</div>
			{#if advancedParams.channels && advancedParams.channels.length > 0}
				<div class="filter-chips">
					{#each advancedParams.channels as channel}
						<span class="filter-chip">
							#{channel}
							<button onclick={() => removeChannelFilter(channel)}>×</button>
						</span>
					{/each}
				</div>
			{/if}
		</div>
	{/if}

	<!-- User Filters -->
	{#if useAdvancedMode}
		<div class="filters-section">
			<h4>User Filters</h4>
			<div class="filter-input-group">
				<input
					type="text"
					placeholder="Add username..."
					onkeydown={(e) => {
						if (e.key === 'Enter') {
							e.preventDefault();
							const input = e.target as HTMLInputElement;
							if (input.value.trim()) {
								addUserFilter(input.value.trim());
								input.value = '';
							}
						}
					}}
				/>
			</div>
			{#if advancedParams.users && advancedParams.users.length > 0}
				<div class="filter-chips">
					{#each advancedParams.users as user}
						<span class="filter-chip">
							{user}
							<button onclick={() => removeUserFilter(user)}>×</button>
						</span>
					{/each}
				</div>
			{/if}
		</div>
	{/if}

	<!-- Search Results -->
	<SearchResults
		results={$searchResultsQuery.data}
		isLoading={$searchResultsQuery.isPending}
		error={$searchResultsQuery.error}
		searchQuery={useAdvancedMode ? advancedParams.q : searchQuery}
		isGlobalSearch={true}
		on:clearSearch={clearSearch}
	/>
</div>

<style>
	.container {
		padding: 1rem;
		max-width: 1400px;
		margin: 0 auto;
	}

	.search-header {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		color: white;
		padding: 2rem;
		border-radius: 12px;
		margin-bottom: 1.5rem;
	}

	.header-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		gap: 1rem;
	}

	.title-section h1 {
		margin: 0 0 0.5rem 0;
		font-size: 2.5rem;
		font-weight: 700;
	}

	.title-section p {
		margin: 0;
		opacity: 0.9;
		font-size: 1.1rem;
	}

	.mode-toggle {
		display: flex;
		align-items: center;
		gap: 1rem;
	}

	.switch {
		position: relative;
		display: flex;
		align-items: center;
		gap: 0.75rem;
		cursor: pointer;
	}

	.switch input {
		opacity: 0;
		width: 0;
		height: 0;
	}

	.slider {
		position: relative;
		width: 50px;
		height: 24px;
		background-color: rgba(255, 255, 255, 0.3);
		border-radius: 24px;
		transition: 0.3s;
	}

	.slider:before {
		position: absolute;
		content: '';
		height: 18px;
		width: 18px;
		left: 3px;
		top: 3px;
		background-color: white;
		border-radius: 50%;
		transition: 0.3s;
	}

	.switch input:checked + .slider {
		background-color: var(--theme);
	}

	.switch input:checked + .slider:before {
		transform: translateX(26px);
	}

	.switch .label {
		font-weight: 500;
		font-size: 1rem;
	}

	.info-panel {
		background: var(--bg-bright);
		border: 1px solid var(--theme2);
		border-radius: 8px;
		padding: 1rem;
		margin-bottom: 1.5rem;
		color: var(--text);
	}

	.search-section {
		background: var(--bg-bright);
		border-radius: 12px;
		padding: 1.5rem;
		margin-bottom: 1.5rem;
		border: 1px solid var(--border-color);
	}

	.search-input-group {
		display: flex;
		gap: 0.75rem;
		align-items: center;
		margin-bottom: 1rem;
	}

	.search-input {
		flex: 1;
		padding: 0.75rem 1rem;
		font-size: 1rem;
		border: 2px solid var(--border-color);
		border-radius: 8px;
		background: var(--bg-dark);
		color: var(--text);
		transition: border-color 0.2s;
	}

	.search-input:focus {
		outline: none;
		border-color: var(--theme2);
	}

	.advanced-toggle {
		padding: 0.75rem 1rem;
		background: var(--bg-dark);
		border: 2px solid var(--border-color);
		border-radius: 8px;
		color: var(--text);
		cursor: pointer;
		transition: all 0.2s;
	}

	.advanced-toggle:hover {
		border-color: var(--theme2);
	}

	.advanced-toggle.active {
		background: var(--theme2);
		border-color: var(--theme2);
		color: white;
	}

	.advanced-fields {
		background: var(--bg-dark);
		border-radius: 8px;
		padding: 1.5rem;
		margin-top: 1rem;
		border: 1px solid var(--border-color);
	}

	.field-group {
		margin-bottom: 1.5rem;
	}

	.field-group:last-child {
		margin-bottom: 0;
	}

	.field-group h4 {
		margin: 0 0 0.75rem 0;
		color: var(--theme2);
		font-size: 1rem;
		font-weight: 600;
	}

	.checkbox-group {
		display: flex;
		flex-wrap: wrap;
		gap: 1rem;
	}

	.checkbox-group label {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		cursor: pointer;
		font-size: 0.9rem;
	}

	.date-inputs,
	.number-inputs {
		display: flex;
		gap: 0.75rem;
		flex-wrap: wrap;
	}

	.date-inputs input,
	.number-inputs input {
		flex: 1;
		min-width: 200px;
	}

	.filters-section {
		background: var(--bg-bright);
		border-radius: 8px;
		padding: 1rem;
		margin-bottom: 1rem;
		border: 1px solid var(--border-color);
	}

	.filters-section h4 {
		margin: 0 0 0.75rem 0;
		color: var(--theme2);
		font-size: 1rem;
		font-weight: 600;
	}

	.filter-input-group {
		margin-bottom: 0.75rem;
	}

	.filter-input-group select,
	.filter-input-group input {
		width: 100%;
		max-width: 300px;
	}

	.filter-chips {
		display: flex;
		flex-wrap: wrap;
		gap: 0.5rem;
	}

	.filter-chip {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.25rem 0.75rem;
		background: var(--theme2);
		color: white;
		border-radius: 99px;
		font-size: 0.875rem;
	}

	.filter-chip button {
		background: none;
		border: none;
		color: white;
		cursor: pointer;
		padding: 0;
		font-size: 1.2rem;
		line-height: 1;
		opacity: 0.8;
	}

	.filter-chip button:hover {
		opacity: 1;
	}
</style>